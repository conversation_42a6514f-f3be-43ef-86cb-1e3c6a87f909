<template>
  <div>
    <div> 干员组干员 </div>
    <div class="my-5 mr-5">
      <ACard size="small" :class="`${prefixCls}__card`">
        <ATypographyParagraph
          :class="`${prefixCls}__card-content`"
          :ellipsis="ellipsis ? { rows: 1, onEllipsis: handleOnEllipsis } : {}"
          :content="formatUserList(memberList, '暂无')"
          class="mr-15"
        />
        <a-button
          v-if="showEllipsisButton"
          type="link"
          size="small"
          :class="`${prefixCls}__card-ellipsis-btn`"
          @click="ellipsis = !ellipsis"
        >
          {{ !ellipsis ? '收起' : '展开' }}
          <BasicArrow class="ml-1" :expand="ellipsis" up />
        </a-button>
      </ACard>
    </div>
    <div>干员组权限</div>
    <div class="h-[calc(100vh_-_460px)] min-h-300px overflow-hidden">
      <BasicVxeTable :loading="loading" :options="gridOptions" />
    </div>
  </div>
</template>

<script lang="tsx" setup>
import {
  Card as ACard,
  TypographyParagraph as ATypographyParagraph,
  Checkbox,
  Col,
  Row,
  Tooltip,
} from 'ant-design-vue';
import { computed, ref, unref } from 'vue';
import type {
  EditProjectPermissionParamsItem,
  ProjectPermissionChildrenListItem,
  ProjectPermissionListItem,
} from '/@/api/page/model/systemModel';
import {
  editProjectPermission,
  getProjectMemberListByPage,
  getProjectPermissionList,
} from '/@/api/page/system';
import type { UserInfoModel } from '/@/api/sys/model/userModel';
import { BasicArrow } from '/@/components/Basic';
import { formatUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useRouter } from 'vue-router';
import { type VxeGridProps, BasicVxeTable } from '@hg-tech/oasis-common';
import row from 'ant-design-vue/es/row';
import { useLatestPromise } from '@hg-tech/utils-vue';

const props = defineProps({
  curGroup: {
    type: String,
    default: '',
  },
  curGroupName: {
    type: String,
    default: '',
  },
  isAdmin: {
    type: Boolean,
    default: () => false,
  },
});

const { prefixCls } = useDesign('project-permission-list');
const { currentRoute } = useRouter();
const projectID = Number(currentRoute.value.params.id);
const permissionList = ref<ProjectPermissionListItem[]>([]);
const ellipsis = ref(true);
const showEllipsisButton = ref(false);
const memberList = ref<UserInfoModel[]>([]);
const allChecked = ref(false);
const allIndeterminate = ref(false);
const { execute, loading } = useLatestPromise(getProjectPermissionList);

const gridOptions = computed(() => ({
  height: 'auto',
  virtualYConfig: {
    enabled: true,
    gt: 20,
  },
  columns: [
    { type: '', width: 60, slots: {
      default({ row }) {
        return (
          <Checkbox
            checked={row.checked}
            disabled={props.isAdmin === false || props.curGroupName === '管理员'}
            indeterminate={row.indeterminate}
            onChange={(e) => {
              row.checked = e.target.checked;
              onSelect({ row, checked: e.target.checked });
            }}
          />
        );
      },
      header() {
        return (
          <Checkbox
            checked={allChecked.value}
            disabled={props.isAdmin === false || props.curGroupName === '管理员'}
            indeterminate={allIndeterminate.value}
            onChange={(e) => {
              row.checked = e.target.checked;
              onSelectAll(e.target.checked);
            }}
          />
        );
      },
    } },
    { field: 'name', title: '类型', width: 200 },
    {
      title: '权限内容',
      field: 'children',
      align: 'left',
      slots: {
        default({ row }) {
          return (
            <Row gutter={[8, 8]} style={{ padding: '10px 0' }}>
              {row.children?.map((item: any) => (
                <Col
                  key={item.ID}
                  lg={10}
                  md={12}
                  sm={24}
                  xl={8}
                  xs={24}
                  xxl={4}
                >
                  <div class="flex gap-6px">
                    <Checkbox
                      checked={item.checked}
                      disabled={props.isAdmin === false || props.curGroupName === '管理员'}
                      onChange={(e) => {
                        item.checked = e.target.checked;
                        checkboxChange(row);
                      }}
                    />
                    <Tooltip title={item.description}>
                      <div
                        style={{ maxWidth: '7vw', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                      >
                        {item.description}
                      </div>
                    </Tooltip>
                  </div>
                </Col>
              ))}
            </Row>

          );
        },
      },
    },
  ],
  checkboxConfig: {
    checkField: 'checked',
    indeterminateField: 'indeterminate',
    reserve: true,

  },
  data: permissionList.value,
  striped: false,
}) as VxeGridProps<ProjectPermissionListItem>);

function setSelectAll() {
  let selectGroupCount = 0;
  permissionList.value.forEach((e: ProjectPermissionListItem) => {
    if (e.checked) {
      selectGroupCount++;
    }
  });
  if (selectGroupCount === permissionList.value.length) {
    allChecked.value = true;
  } else {
    allChecked.value = false;
  }
  if (selectGroupCount > 0 && selectGroupCount < permissionList.value.length) {
    allIndeterminate.value = true;
  } else {
    allIndeterminate.value = false;
  }
}
async function getMemberList() {
  const { list } = await getProjectMemberListByPage(
    projectID,
    unref(props.curGroup),
    {
      page: 1,
      pageSize: 999,
    },
  );

  if (list?.length > 0) {
    memberList.value = list;
  } else {
    memberList.value = [];
  }
}

getMemberList();

// 获取当前角色组权限列表
async function getList() {
  const { apis } = await execute(
    projectID,
    unref(props.curGroup),
  );
  apis.forEach((e: ProjectPermissionListItem, i: number) => {
    let selectCount = 0;

    e.children?.forEach((child: ProjectPermissionChildrenListItem, i: number) => {
      child.value = child;
      if (child.checked && child.ID) {
        selectCount++;
      }

      if (e.children && i === e.children.length - 1) {
        if (selectCount === e.children.length) {
          e.checked = true;
        } else {
          e.checked = false;
        }
        if (selectCount > 0 && selectCount < e.children.length) {
          e.indeterminate = true;
        } else {
          e.indeterminate = false;
        }

        selectCount = 0;
      }
    });
  });
  permissionList.value = apis;
  setSelectAll();
}

getList();

function onSelect({ row, checked }: { row: ProjectPermissionListItem; checked: boolean }) {
  row.indeterminate = false;
  row.children?.forEach((item: ProjectPermissionChildrenListItem) => item.checked = checked);
  setSelectAll();
  editPermission();
}

/**
 * 列表全选中事件
 * @param selected 选择状态
 */
function onSelectAll(selected: boolean) {
  allIndeterminate.value = false;
  allChecked.value = selected;
  if (selected) {
    permissionList.value.forEach((e: ProjectPermissionListItem) => {
      e.checked = true;
      e.indeterminate = false;
      e.children?.forEach((item: ProjectPermissionChildrenListItem) => item.checked = true);
    });
  } else {
    permissionList.value.forEach((e: ProjectPermissionListItem) => {
      e.checked = false;
      e.indeterminate = false;
      e.children?.forEach((item: ProjectPermissionChildrenListItem) => item.checked = false);
    });
  }
  editPermission();
}

/**
 * 单个权限变更事件
 */
function checkboxChange(row: ProjectPermissionListItem) {
  let selectCount = 0;
  row.children?.forEach((item: ProjectPermissionChildrenListItem) => {
    if (item.checked) {
      selectCount++;
    }
  });
  if (selectCount === row.children?.length) {
    row.checked = true;
  } else {
    row.checked = false;
  }
  if (selectCount > 0 && selectCount < row.children?.length) {
    row.indeterminate = true;
  } else {
    row.indeterminate = false;
  }
  setSelectAll();
  editPermission();
}

// 选择有变化调用编辑权限接口
async function editPermission() {
  let submitList: EditProjectPermissionParamsItem[] = [];

  permissionList.value.forEach((e) => {
    const list: EditProjectPermissionParamsItem[] = [];

    e.children?.forEach((item) => {
      if (item.checked) {
        const { method, path } = item;
        list.push({ method, path });
      }
    });
    submitList = submitList.concat(list);
  });

  await editProjectPermission(projectID, unref(props.curGroup), {
    casbin_infos: submitList,
  });
}

function handleOnEllipsis(ellipsis: boolean) {
  showEllipsisButton.value = ellipsis;
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-project-permission-list';
.@{prefix-cls} {
  &__card {
    background-color: @FO-Container-Fill2;
    position: relative;

    &-content {
      margin-bottom: 0 !important;
    }

    &-ellipsis-btn {
      position: absolute;
      right: 5px;
      bottom: 11px;
    }
  }

  &__table {
    .ant-table-row-selected > td.ant-table-cell {
      background: none !important;
    }
  }
}
</style>
